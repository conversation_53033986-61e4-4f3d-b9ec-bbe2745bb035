.game-reminder-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: flex-start; /* Align to left side where players table is */
  align-items: center;
  padding-left: 5%; /* Add some padding from the left edge */
  z-index: 9999; /* Much higher z-index to ensure it's above everything */
  animation: fadeIn 0.3s ease-out;
}

/* Additional dimming effect for better visibility */
.game-reminder-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: -1;
  pointer-events: none;
}

.game-reminder-modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px; /* Slightly smaller for left positioning */
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInScale 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10000; /* Ensure modal is above the overlay */
}

.game-reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #673AB7, #9C27B0);
  color: white;
  border-radius: 12px 12px 0 0;
}

.game-reminder-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.game-reminder-close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: white;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.game-reminder-close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.game-reminder-content {
  padding: 24px;
  text-align: center;
}

.game-reminder-content p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #444;
}

.game-reminder-footer {
  padding: 16px 24px 24px 24px;
  text-align: center;
  border-top: 1px solid #eee;
}

.game-reminder-button {
  background: linear-gradient(135deg, #673AB7, #9C27B0);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(103, 58, 183, 0.3);
}

.game-reminder-button:hover {
  background: linear-gradient(135deg, #5E35B1, #8E24AA);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 58, 183, 0.4);
}

.game-reminder-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(103, 58, 183, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .game-reminder-overlay {
    justify-content: center; /* Center on mobile */
    padding-left: 0;
  }

  .game-reminder-modal {
    width: 95%;
    margin: 20px;
  }

  .game-reminder-header {
    padding: 16px 20px 12px 20px;
  }

  .game-reminder-header h3 {
    font-size: 1.2rem;
  }

  .game-reminder-content {
    padding: 20px;
  }

  .game-reminder-content p {
    font-size: 1rem;
  }

  .game-reminder-footer {
    padding: 12px 20px 20px 20px;
  }

  .game-reminder-button {
    padding: 10px 24px;
    font-size: 0.95rem;
  }
}
