import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import './GameReminderOverlay.css';

const GameReminderOverlay = ({ isVisible, onClose }) => {
  const { t } = useTranslation();
  const overlayRef = useRef(null);

  // <PERSON>le click outside to close overlay
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (overlayRef.current && !overlayRef.current.contains(event.target)) {
        // Check if the click is on the teams-container or its children
        const teamsContainer = document.querySelector('.teams-container');
        if (teamsContainer && teamsContainer.contains(event.target)) {
          // Don't close if clicking on teams container
          return;
        }
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent scrolling when overlay is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'auto';
    };
  }, [isVisible, onClose]);

  // Handle ESC key to close overlay
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div className="game-reminder-overlay">
      <div className="game-reminder-modal" ref={overlayRef}>
        <div className="game-reminder-header">
          <h3>{t('gameReminder.title')}</h3>
          <button className="game-reminder-close-button" onClick={onClose}>×</button>
        </div>
        <div className="game-reminder-content">
          <p>{t('gameReminder.message')}</p>
        </div>
        <div className="game-reminder-footer">
          <button className="game-reminder-button" onClick={onClose}>
            {t('gameReminder.close')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameReminderOverlay;
