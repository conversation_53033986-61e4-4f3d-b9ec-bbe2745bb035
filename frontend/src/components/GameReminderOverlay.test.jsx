import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import GameReminderOverlay from './GameReminderOverlay';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        'gameReminder.title': 'Game Finished?',
        'gameReminder.message': "It's been 10 minutes since teams were balanced. Don't forget to select the winning team!",
        'gameReminder.close': 'Got it!'
      };
      return translations[key] || key;
    }
  })
}));

describe('GameReminderOverlay Component', () => {
  const defaultProps = {
    isVisible: false,
    onClose: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when isVisible is false', () => {
    render(<GameReminderOverlay {...defaultProps} />);
    expect(screen.queryByText('Game Finished?')).not.toBeInTheDocument();
  });

  it('renders correctly when isVisible is true', () => {
    render(<GameReminderOverlay {...defaultProps} isVisible={true} />);
    
    expect(screen.getByText('Game Finished?')).toBeInTheDocument();
    expect(screen.getByText("It's been 10 minutes since teams were balanced. Don't forget to select the winning team!")).toBeInTheDocument();
    expect(screen.getByText('Got it!')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const onCloseMock = vi.fn();
    render(<GameReminderOverlay {...defaultProps} isVisible={true} onClose={onCloseMock} />);
    
    const closeButton = screen.getByText('×');
    fireEvent.click(closeButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when "Got it!" button is clicked', () => {
    const onCloseMock = vi.fn();
    render(<GameReminderOverlay {...defaultProps} isVisible={true} onClose={onCloseMock} />);
    
    const gotItButton = screen.getByText('Got it!');
    fireEvent.click(gotItButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when ESC key is pressed', () => {
    const onCloseMock = vi.fn();
    render(<GameReminderOverlay {...defaultProps} isVisible={true} onClose={onCloseMock} />);
    
    fireEvent.keyDown(document, { key: 'Escape' });
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('prevents body scrolling when overlay is visible', () => {
    const { rerender } = render(<GameReminderOverlay {...defaultProps} isVisible={true} />);
    
    expect(document.body.style.overflow).toBe('hidden');
    
    rerender(<GameReminderOverlay {...defaultProps} isVisible={false} />);
    
    expect(document.body.style.overflow).toBe('auto');
  });
});
